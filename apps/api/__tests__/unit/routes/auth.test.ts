import Fastify, { type FastifyInstance } from 'fastify';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { authConfig } from '../../../src/config/auth.js';
import { OAuthProvider } from '../../../src/config/auth.js';
import type { User } from '../../../src/database/types.js';
import { authRoutes } from '../../../src/routes/auth.js';
import * as authService from '../../../src/features/auth/auth.service.js';

// Mock the auth service
vi.mock('../../../src/features/auth/auth.service.js', () => ({
  handleOAuthLogin: vi.fn(),
}));

// Helper function to create mock user
function createMockUser(overrides: Partial<User> = {}): User {
  return {
    id: 'user-123',
    email: '<EMAIL>',
    passwordHash: null,
    firstName: 'Test',
    lastName: 'User',
    role: 'user',
    emailVerified: true,
    phoneNumber: null,
    phoneVerified: false,
    lastLoginAt: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    isDeleted: false,
    ...overrides,
  };
}

describe('Unit Tests - Auth Routes', () => {
  let app: FastifyInstance;
  const mockHandleOAuthLogin = vi.mocked(authService.handleOAuthLogin);
  let originalEnv: NodeJS.ProcessEnv;

  beforeEach(async () => {
    // Save original environment
    originalEnv = { ...process.env };

    // Set required environment variables for tests
    process.env.NODE_ENV = 'test';
    process.env.JWT_SECRET =
      'test-jwt-secret-that-is-at-least-32-characters-long';

    // Create a fresh Fastify instance for each test
    app = Fastify({ logger: false });

    // Mock the setCookie method
    app.decorateReply(
      'setCookie',
      function (name: string, value: string, options: any) {
        this.header(
          'Set-Cookie',
          `${name}=${value}; ${Object.entries(options)
            .map(([k, v]) => `${k}=${v}`)
            .join('; ')}`
        );
        return this;
      }
    );

    // Register the auth routes
    await app.register(authRoutes);
    await app.ready();

    // Reset all mocks
    vi.clearAllMocks();
  });

  afterEach(async () => {
    await app.close();
    // Restore original environment
    process.env = originalEnv;
  });

  describe('Google OAuth Routes', () => {
    describe('GET /auth/google', () => {
      it('should redirect to Google OAuth URL in test mode', async () => {
        // Set test environment
        const originalEnv = process.env.NODE_ENV;
        process.env.NODE_ENV = 'test';

        const response = await app.inject({
          method: 'GET',
          url: '/auth/google',
          headers: {
            host: 'localhost:3000',
          },
        });

        expect(response.statusCode).toBe(302);
        expect(response.headers.location).toContain(
          'accounts.google.com/oauth/authorize'
        );
        expect(response.headers.location).toContain('client_id=mock-client-id');
        expect(response.headers.location).toContain('response_type=code');
        expect(response.headers.location).toContain('scope=email%20profile');

        // Restore environment
        process.env.NODE_ENV = originalEnv;
      });

      it('should redirect to mock callback in non-test mode', async () => {
        // Set development environment
        const originalEnv = process.env.NODE_ENV;
        process.env.NODE_ENV = 'development';

        const response = await app.inject({
          method: 'GET',
          url: '/auth/google',
        });

        expect(response.statusCode).toBe(302);
        expect(response.headers.location).toBe(
          '/auth/google/callback?code=mock-auth-code'
        );

        // Restore environment
        process.env.NODE_ENV = originalEnv;
      });
    });

    describe('GET /auth/google/callback', () => {
      it('should handle successful OAuth callback', async () => {
        const mockToken = 'mock-jwt-token';
        const mockUser = createMockUser();

        mockHandleOAuthLogin.mockResolvedValue({
          token: mockToken,
          user: mockUser,
        });

        const response = await app.inject({
          method: 'GET',
          url: '/auth/google/callback?code=auth-code-123',
        });

        expect(response.statusCode).toBe(302);
        expect(response.headers.location).toBe('/');
        expect(response.headers['set-cookie']).toContain(
          `${authConfig.AUTH_COOKIE_NAME}=${mockToken}`
        );

        expect(mockHandleOAuthLogin).toHaveBeenCalledWith({
          id: 'google-123456',
          email: '<EMAIL>',
          firstName: 'Google',
          lastName: 'User',
          provider: OAuthProvider.GOOGLE,
          picture: 'https://example.com/avatar.jpg',
        });
      });

      it('should redirect to mobile app when platform=mobile', async () => {
        const mockToken = 'mock-jwt-token';
        const mockUser = createMockUser();

        mockHandleOAuthLogin.mockResolvedValue({
          token: mockToken,
          user: mockUser,
        });

        const response = await app.inject({
          method: 'GET',
          url: '/auth/google/callback?code=auth-code-123&platform=mobile',
        });

        expect(response.statusCode).toBe(302);
        expect(response.headers.location).toBe(
          `${authConfig.AUTH_MOBILE_REDIRECT_URL}?token=${mockToken}`
        );
      });

      it('should handle OAuth error response', async () => {
        const response = await app.inject({
          method: 'GET',
          url: '/auth/google/callback?error=access_denied&error_description=User%20denied%20access',
        });

        expect(response.statusCode).toBe(302);
        expect(response.headers.location).toBe(
          '/auth/error?error=access_denied'
        );
        expect(mockHandleOAuthLogin).not.toHaveBeenCalled();
      });

      it('should handle missing authorization code', async () => {
        const response = await app.inject({
          method: 'GET',
          url: '/auth/google/callback',
        });

        expect(response.statusCode).toBe(302);
        expect(response.headers.location).toBe('/auth/error?error=no_code');
        expect(mockHandleOAuthLogin).not.toHaveBeenCalled();
      });

      it('should handle OAuth service errors', async () => {
        mockHandleOAuthLogin.mockRejectedValue(
          new Error('OAuth service error')
        );

        const response = await app.inject({
          method: 'GET',
          url: '/auth/google/callback?code=auth-code-123',
        });

        expect(response.statusCode).toBe(302);
        expect(response.headers.location).toBe('/auth/error');
      });
    });
  });

  describe('Apple OAuth Routes', () => {
    describe('GET /auth/apple', () => {
      it('should redirect to Apple OAuth URL in test mode', async () => {
        // Set test environment
        const originalEnv = process.env.NODE_ENV;
        process.env.NODE_ENV = 'test';

        const response = await app.inject({
          method: 'GET',
          url: '/auth/apple',
          headers: {
            host: 'localhost:3000',
          },
        });

        expect(response.statusCode).toBe(302);
        expect(response.headers.location).toContain(
          'appleid.apple.com/auth/authorize'
        );
        expect(response.headers.location).toContain('client_id=mock-client-id');
        expect(response.headers.location).toContain('response_type=code');
        expect(response.headers.location).toContain('scope=email%20name');
        expect(response.headers.location).toContain('response_mode=form_post');

        // Restore environment
        process.env.NODE_ENV = originalEnv;
      });

      it('should redirect to mock callback in non-test mode', async () => {
        // Set development environment
        const originalEnv = process.env.NODE_ENV;
        process.env.NODE_ENV = 'development';

        const response = await app.inject({
          method: 'GET',
          url: '/auth/apple',
        });

        expect(response.statusCode).toBe(302);
        expect(response.headers.location).toBe(
          '/auth/apple/callback?code=mock-auth-code'
        );

        // Restore environment
        process.env.NODE_ENV = originalEnv;
      });
    });

    describe('Apple OAuth Callback (GET and POST)', () => {
      it('should handle successful GET callback', async () => {
        const mockToken = 'mock-jwt-token';
        const mockUser = createMockUser();

        mockHandleOAuthLogin.mockResolvedValue({
          token: mockToken,
          user: mockUser,
        });

        const response = await app.inject({
          method: 'GET',
          url: '/auth/apple/callback?code=auth-code-123',
        });

        expect(response.statusCode).toBe(302);
        expect(response.headers.location).toBe('/');
        expect(response.headers['set-cookie']).toContain(
          `${authConfig.AUTH_COOKIE_NAME}=${mockToken}`
        );

        expect(mockHandleOAuthLogin).toHaveBeenCalledWith({
          id: 'apple-123456',
          email: '<EMAIL>',
          firstName: 'Apple',
          lastName: 'User',
          provider: OAuthProvider.APPLE,
        });
      });

      it('should handle successful POST callback', async () => {
        const mockToken = 'mock-jwt-token';
        const mockUser = createMockUser();

        mockHandleOAuthLogin.mockResolvedValue({
          token: mockToken,
          user: mockUser,
        });

        const response = await app.inject({
          method: 'POST',
          url: '/auth/apple/callback',
          payload: {
            code: 'auth-code-123',
          },
        });

        expect(response.statusCode).toBe(302);
        expect(response.headers.location).toBe('/');
        expect(mockHandleOAuthLogin).toHaveBeenCalled();
      });

      it('should handle POST callback with mobile platform', async () => {
        const mockToken = 'mock-jwt-token';
        const mockUser = createMockUser();

        mockHandleOAuthLogin.mockResolvedValue({
          token: mockToken,
          user: mockUser,
        });

        const response = await app.inject({
          method: 'POST',
          url: '/auth/apple/callback?platform=mobile',
          payload: {
            code: 'auth-code-123',
          },
        });

        expect(response.statusCode).toBe(302);
        expect(response.headers.location).toBe(
          `${authConfig.AUTH_MOBILE_REDIRECT_URL}?token=${mockToken}`
        );
      });

      it('should handle GET callback error', async () => {
        const response = await app.inject({
          method: 'GET',
          url: '/auth/apple/callback?error=user_cancelled&error_description=User%20cancelled%20authentication',
        });

        expect(response.statusCode).toBe(302);
        expect(response.headers.location).toBe(
          '/auth/error?error=user_cancelled'
        );
        expect(mockHandleOAuthLogin).not.toHaveBeenCalled();
      });

      it('should handle POST callback error', async () => {
        const response = await app.inject({
          method: 'POST',
          url: '/auth/apple/callback',
          payload: {
            error: 'invalid_request',
            error_description: 'Invalid request parameters',
          },
        });

        expect(response.statusCode).toBe(302);
        expect(response.headers.location).toBe(
          '/auth/error?error=invalid_request'
        );
        expect(mockHandleOAuthLogin).not.toHaveBeenCalled();
      });

      it('should handle missing code in GET callback', async () => {
        const response = await app.inject({
          method: 'GET',
          url: '/auth/apple/callback',
        });

        expect(response.statusCode).toBe(302);
        expect(response.headers.location).toBe('/auth/error?error=no_code');
      });

      it('should handle missing code in POST callback', async () => {
        const response = await app.inject({
          method: 'POST',
          url: '/auth/apple/callback',
          payload: {},
        });

        expect(response.statusCode).toBe(302);
        expect(response.headers.location).toBe('/auth/error?error=no_code');
      });

      it('should handle service errors in callback', async () => {
        mockHandleOAuthLogin.mockRejectedValue(new Error('Service error'));

        const response = await app.inject({
          method: 'GET',
          url: '/auth/apple/callback?code=auth-code-123',
        });

        expect(response.statusCode).toBe(302);
        expect(response.headers.location).toBe('/auth/error');
      });
    });
  });

  describe('Error Route', () => {
    it('should return error response', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/auth/error',
      });

      expect(response.statusCode).toBe(200);
      expect(response.json()).toEqual({ error: 'Authentication failed' });
    });

    it('should handle error with query parameters', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/auth/error?error=access_denied',
      });

      expect(response.statusCode).toBe(200);
      expect(response.json()).toEqual({ error: 'Authentication failed' });
    });
  });
});
