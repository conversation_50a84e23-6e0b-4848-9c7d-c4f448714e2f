import { existsSync } from 'node:fs';
import { dirname, resolve } from 'node:path';
import { fileURLToPath } from 'node:url';
import { config } from 'dotenv';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from '../schema.js';
import {
  getAccountsInsertData,
  getBudgetsInsertData,
  getCategoriesInsertData,
  getGoalsInsertData,
  getJournalEntriesInsertData,
  getJournalLinesInsertData,
  getUserSettingsInsertData,
  getUsersInsertData,
} from './data/index.js';

// Import test seed data
import { testSeedData } from './seeds/test/index.js';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Determine which .env file to load based on NODE_ENV
const nodeEnv = process.env.NODE_ENV || 'development';
const envFile = nodeEnv === 'test' ? '.env.test' : '.env';

// Load environment variables from .env file
const envPath = resolve(__dirname, `../../${envFile}`);
if (existsSync(envPath)) {
  console.log(
    `Loading environment variables from ${envPath} (${nodeEnv} environment)`
  );
  config({ path: envPath });
}

// Get the database URL from the environment
const databaseUrl = process.env.DATABASE_URL;

// Seed the database
async function main() {
  if (!databaseUrl) {
    throw new Error('DATABASE_URL environment variable is not set');
  }

  console.log('Connecting to database...');
  const client = postgres(databaseUrl, { max: 1 });
  const db = drizzle(client, { schema });

  try {
    console.log('Starting database seeding...');

    // Clear existing data
    console.log('Clearing existing data...');
    await db.delete(schema.goals);
    await db.delete(schema.budgets);
    await db.delete(schema.journalLines);
    await db.delete(schema.journalEntries);
    await db.delete(schema.categories);
    await db.delete(schema.accounts);
    await db.delete(schema.userSettings);
    await db.delete(schema.users);
    console.log('Existing data cleared.');

    // Determine which seed data to use based on environment
    if (process.env.NODE_ENV === 'test') {
      console.log('Using test seed data...');

      // Insert test users
      console.log('Inserting test users...');
      await db.insert(schema.users).values(testSeedData.users);

      // Insert test user settings
      console.log('Inserting test user settings...');
      await db.insert(schema.userSettings).values(testSeedData.userSettings);

      // Insert test accounts
      console.log('Inserting test accounts...');
      await db.insert(schema.accounts).values(testSeedData.accounts);

      // Insert test categories
      console.log('Inserting test categories...');
      await db.insert(schema.categories).values(testSeedData.categories);

      // Insert test journal entries (transactions)
      console.log('Inserting test journal entries...');
      await db
        .insert(schema.journalEntries)
        .values(testSeedData.journalEntries);

      // Insert test journal lines (transaction details)
      console.log('Inserting test journal lines...');
      await db.insert(schema.journalLines).values(testSeedData.journalLines);
    } else {
      // Insert users
      console.log('Inserting users...');
      const usersData = getUsersInsertData();
      await db.insert(schema.users).values(usersData);

      // Insert user settings
      console.log('Inserting user settings...');
      const userSettingsData = getUserSettingsInsertData();
      await db.insert(schema.userSettings).values(userSettingsData);

      // Insert accounts
      console.log('Inserting accounts...');
      const accountsData = getAccountsInsertData();
      await db.insert(schema.accounts).values(accountsData);

      // Insert categories
      console.log('Inserting categories...');
      const categoriesData = getCategoriesInsertData();
      await db.insert(schema.categories).values(categoriesData);

      // Insert journal entries (transactions)
      console.log('Inserting journal entries...');
      const journalEntriesData = getJournalEntriesInsertData();
      await db.insert(schema.journalEntries).values(journalEntriesData);

      // Insert journal lines (transaction details)
      console.log('Inserting journal lines...');
      const journalLinesData = getJournalLinesInsertData();
      await db.insert(schema.journalLines).values(journalLinesData);

      // Insert budgets
      console.log('Inserting budgets...');
      const budgetsData = getBudgetsInsertData();
      await db.insert(schema.budgets).values(budgetsData);

      // Insert goals
      console.log('Inserting goals...');
      const goalsData = getGoalsInsertData();
      await db.insert(schema.goals).values(goalsData);
    }

    console.log('Database seeding completed successfully!');
  } catch (error) {
    console.error('Error seeding database:');
    console.error(error);
    process.exit(1);
  } finally {
    // Close the database connection
    await client.end();
  }

  process.exit(0);
}

main().catch(error => {
  console.error('Seeding failed:');
  console.error(error);
  process.exit(1);
});
