// GraphQL client for testing
import type { FastifyInstance } from 'fastify';
import { createServer } from '../../server.js';
import {
  createTestAdminWithToken,
  createTestUserWithToken,
} from './auth-helpers.js';

interface GraphQLRequest {
  query: string;
  variables?: Record<string, unknown>;
  operationName?: string;
}

interface GraphQLResponse<T = unknown> {
  data?: T;
  errors?: Array<{
    message: string;
    locations?: Array<{ line: number; column: number }>;
    path?: string[];
    extensions?: Record<string, unknown>;
  }>;
  status?: number;
  headers?: Record<string, string>;
}

/**
 * A utility for making GraphQL requests to the API server in tests
 */
export class TestGraphQLClient {
  private app: FastifyInstance;
  private token: string | null = null;
  private cookies: Record<string, string> = {};
  private headers: Record<string, string | undefined> = {
    'content-type': 'application/json',
  };
  private user: unknown = null;

  constructor(app: FastifyInstance) {
    this.app = app;
  }

  /**
   * Set the authentication token for subsequent requests
   */
  setToken(token: string | null) {
    this.token = token;
    if (token) {
      this.headers.authorization = `Bearer ${token}`;
    } else {
      this.headers.authorization = undefined;
    }
    return this;
  }

  /**
   * Set a cookie for subsequent requests
   */
  setCookie(name: string, value: string) {
    this.cookies[name] = value;
    return this;
  }

  /**
   * Set a header for subsequent requests
   */
  setHeader(name: string, value: string) {
    this.headers[name] = value;
    return this;
  }

  /**
   * Set the authenticated user for subsequent requests
   */
  setUser(user: unknown) {
    this.user = user;
    return this;
  }

  /**
   * Get the current authenticated user
   */
  getUser() {
    return this.user;
  }

  /**
   * Get the current authentication token
   */
  getToken() {
    return this.token;
  }

  /**
   * Create a regular user and set the token
   */
  async loginAsUser(overrides: Partial<Record<string, unknown>> = {}) {
    const { user, token } = await createTestUserWithToken(overrides);
    this.setToken(token);
    this.setUser(user);
    return { user, token };
  }

  /**
   * Create an admin user and set the token
   */
  async loginAsAdmin(overrides: Partial<Record<string, unknown>> = {}) {
    const { user, token } = await createTestAdminWithToken(overrides);
    this.setToken(token);
    this.setUser(user);
    return { user, token };
  }

  /**
   * Logout by clearing the token and user
   */
  logout() {
    this.setToken(null);
    this.user = null;
    return this;
  }

  /**
   * Make a GraphQL query request
   */
  async query<T = unknown>(
    query: string,
    variables?: Record<string, unknown>,
    operationName?: string
  ): Promise<GraphQLResponse<T>> {
    return this.request<T>({ query, variables, operationName });
  }

  /**
   * Make a GraphQL mutation request
   */
  async mutate<T = unknown>(
    mutation: string,
    variables?: Record<string, unknown>,
    operationName?: string
  ): Promise<GraphQLResponse<T>> {
    return this.request<T>({ query: mutation, variables, operationName });
  }

  /**
   * Make a raw GraphQL request
   */
  private async request<T = unknown>(
    request: GraphQLRequest
  ): Promise<GraphQLResponse<T>> {
    // Prepare cookies
    const cookieHeader = Object.entries(this.cookies)
      .map(([name, value]) => `${name}=${value}`)
      .join('; ');

    if (cookieHeader) {
      this.headers.cookie = cookieHeader;
    }

    // Filter out undefined headers
    const filteredHeaders = Object.fromEntries(
      Object.entries(this.headers).filter(([, value]) => value !== undefined)
    ) as Record<string, string>;

    // Make the request
    const response = await this.app.inject({
      method: 'POST',
      url: '/graphql',
      headers: filteredHeaders,
      payload: JSON.stringify(request),
    });

    // Parse response cookies
    const setCookieHeaders = response.headers['set-cookie'];
    if (setCookieHeaders) {
      const cookies = Array.isArray(setCookieHeaders)
        ? setCookieHeaders
        : [setCookieHeaders];
      for (const cookie of cookies) {
        const [nameValue] = cookie.split(';');
        const [name, value] = nameValue.split('=');
        this.cookies[name] = value;
      }
    }

    // Return the response
    return {
      ...JSON.parse(response.body),
      status: response.statusCode,
      headers: response.headers as Record<string, string>,
    } as GraphQLResponse<T>;
  }

  /**
   * Check if the response has errors
   */
  static hasErrors(response: GraphQLResponse): boolean {
    return (
      !!response.errors &&
      Array.isArray(response.errors) &&
      response.errors.length > 0
    );
  }

  /**
   * Get the first error from a response
   */
  static getFirstError(response: GraphQLResponse): string | null {
    if (
      !response.errors ||
      !Array.isArray(response.errors) ||
      response.errors.length === 0
    ) {
      return null;
    }
    return response.errors[0]?.message || 'Unknown error';
  }

  /**
   * Assert that a response has no errors
   */
  static assertNoErrors(response: GraphQLResponse): void {
    if (
      response.errors &&
      Array.isArray(response.errors) &&
      response.errors.length > 0
    ) {
      throw new Error(
        `GraphQL response has errors: ${JSON.stringify(response.errors)}`
      );
    }
  }

  /**
   * Assert that a response has a specific error
   */
  static assertHasError(response: GraphQLResponse, errorMessage: string): void {
    if (
      !response.errors ||
      !Array.isArray(response.errors) ||
      response.errors.length === 0
    ) {
      throw new Error(
        `Expected GraphQL response to have error "${errorMessage}" but it has no errors`
      );
    }

    const hasMatchingError = response.errors.some(error =>
      error?.message?.includes(errorMessage)
    );

    if (!hasMatchingError) {
      throw new Error(
        `Expected GraphQL response to have error "${errorMessage}" but found: ${JSON.stringify(response.errors)}`
      );
    }
  }

  /**
   * Assert that a response has a specific error code
   */
  static assertHasErrorCode(
    response: GraphQLResponse,
    errorCode: string
  ): void {
    if (
      !response.errors ||
      !Array.isArray(response.errors) ||
      response.errors.length === 0
    ) {
      throw new Error(
        `Expected GraphQL response to have error code "${errorCode}" but it has no errors`
      );
    }

    const hasMatchingError = response.errors.some(
      error => error?.extensions?.code === errorCode
    );

    if (!hasMatchingError) {
      throw new Error(
        `Expected GraphQL response to have error code "${errorCode}" but found: ${JSON.stringify(response.errors)}`
      );
    }
  }
}

/**
 * Create a test GraphQL client
 */
export const createTestClient = async (): Promise<TestGraphQLClient> => {
  const app = await createServer();
  return new TestGraphQLClient(app);
};

/**
 * Create a test GraphQL client with an existing app instance
 */
export const createTestClientWithApp = (
  app: FastifyInstance
): TestGraphQLClient => {
  return new TestGraphQLClient(app);
};

/**
 * Create a test GraphQL client with a logged-in user
 */
export const createTestClientWithUser = async (
  overrides: Partial<Record<string, unknown>> = {}
): Promise<{
  client: TestGraphQLClient;
  user: unknown;
  token: string;
}> => {
  const client = await createTestClient();
  const { user, token } = await client.loginAsUser(overrides);
  return { client, user, token };
};

/**
 * Create a test GraphQL client with a logged-in admin
 */
export const createTestClientWithAdmin = async (
  overrides: Partial<Record<string, unknown>> = {}
): Promise<{
  client: TestGraphQLClient;
  user: unknown;
  token: string;
}> => {
  const client = await createTestClient();
  const { user, token } = await client.loginAsAdmin(overrides);
  return { client, user, token };
};

/**
 * Common GraphQL queries and mutations for testing
 */
export const gqlQueries = {
  // Auth queries
  me: `
    query Me {
      me {
        id
        email
        firstName
        lastName
        role
        emailVerified
        createdAt
        updatedAt
      }
    }
  `,

  // Auth mutations
  register: `
    mutation Register($input: RegisterInput!) {
      register(input: $input) {
        token
        user {
          id
          email
          firstName
          lastName
          role
          emailVerified
          createdAt
          updatedAt
        }
      }
    }
  `,

  login: `
    mutation Login($input: LoginInput!) {
      login(input: $input) {
        token
        user {
          id
          email
          firstName
          lastName
          role
          emailVerified
          createdAt
          updatedAt
        }
      }
    }
  `,

  refreshToken: `
    mutation RefreshToken {
      refreshToken {
        token
        user {
          id
          email
          firstName
          lastName
          role
          emailVerified
          createdAt
          updatedAt
        }
      }
    }
  `,

  logout: `
    mutation Logout {
      logout
    }
  `,

  // Account queries
  getAccounts: `
    query GetAccounts {
      accounts {
        id
        name
        type
        currency
        currentBalance
        initialBalance
        isArchived
        includeInNetWorth
      }
    }
  `,

  getAccount: `
    query GetAccount($id: ID!) {
      account(id: $id) {
        id
        name
        type
        currency
        currentBalance
        initialBalance
        isArchived
        includeInNetWorth
        notes
        icon
        color
        displayOrder
        createdAt
        updatedAt
      }
    }
  `,

  // Account mutations
  createAccount: `
    mutation CreateAccount($input: CreateAccountInput!) {
      createAccount(input: $input) {
        id
        name
        type
        currency
        initialBalance
        currentBalance
        isArchived
        includeInNetWorth
      }
    }
  `,

  updateAccount: `
    mutation UpdateAccount($id: ID!, $input: UpdateAccountInput!) {
      updateAccount(id: $id, input: $input) {
        id
        name
        type
        currency
        initialBalance
        currentBalance
        isArchived
        includeInNetWorth
      }
    }
  `,

  deleteAccount: `
    mutation DeleteAccount($id: ID!) {
      deleteAccount(id: $id)
    }
  `,

  // Category queries
  getCategories: `
    query GetCategories {
      categories {
        id
        name
        type
        parentId
        icon
        color
        isDefault
        isSystem
        isArchived
        displayOrder
      }
    }
  `,

  getCategory: `
    query GetCategory($id: ID!) {
      category(id: $id) {
        id
        name
        type
        parentId
        icon
        color
        isDefault
        isSystem
        isArchived
        displayOrder
        createdAt
        updatedAt
      }
    }
  `,

  // Transaction queries
  getTransactions: `
    query GetTransactions($filter: TransactionFilterInput) {
      transactions(filter: $filter) {
        id
        date
        description
        amount
        type
        categoryId
        accountId
        notes
        tags
      }
    }
  `,

  getTransaction: `
    query GetTransaction($id: ID!) {
      transaction(id: $id) {
        id
        date
        description
        amount
        type
        categoryId
        accountId
        notes
        tags
        createdAt
        updatedAt
      }
    }
  `,
};
