import { existsSync } from 'node:fs';
import { resolve } from 'node:path';
import { config } from 'dotenv';
import type { Config } from 'drizzle-kit';

// Determine which .env file to load based on NODE_ENV
const nodeEnv = process.env.NODE_ENV || 'development';
const envFile = nodeEnv === 'test' ? '.env.test' : '.env';

// Load environment variables from .env file
const envPath = resolve(__dirname, envFile);
if (existsSync(envPath)) {
  console.log(
    `Loading environment variables from ${envPath} (${nodeEnv} environment)`
  );
  config({ path: envPath });
}

export default {
  schema: './src/shared/database/schema.ts',
  out: './migrations',
  dialect: 'postgresql',
  dbCredentials: {
    url: process.env.DATABASE_URL || '',
  },
  verbose: true,
} satisfies Config;
