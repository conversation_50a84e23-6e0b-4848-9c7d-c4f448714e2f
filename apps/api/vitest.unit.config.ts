import { resolve } from 'node:path';
import { defineConfig } from 'vitest/config';

export default defineConfig({
  test: {
    name: 'unit',
    root: resolve(__dirname),
    environment: 'node',
    globals: true,

    // Setup file for unit tests
    setupFiles: ['./src/shared/test-utils/setup.ts'],

    // Set environment variables for unit tests
    env: {
      TEST_SUITE_TYPE: 'unit',
      DATABASE_URL: 'postgresql://fake:fake@localhost:5432/fake_test_db',
      JWT_SECRET: 'fake-jwt-secret-for-unit-tests-only-32-chars-long',
      JWT_EXPIRES_IN: '1h',
      AUTH_COOKIE_NAME: 'budapp_auth_test',
      AUTH_COOKIE_SECURE: 'false',
      AUTH_COOKIE_HTTP_ONLY: 'true',
      AUTH_COOKIE_DOMAIN: 'localhost',
      AUTH_COOKIE_MAX_AGE: '86400',
      AUTH_REDIRECT_URL: 'http://localhost:3001/auth/callback',
      AUTH_MOBILE_REDIRECT_URL: 'budapp://auth/callback',
      GOOGLE_CLIENT_ID: 'fake-google-client-id',
      GOOGLE_CLIENT_SECRET: 'fake-google-client-secret',
      APPLE_CLIENT_ID: 'fake-apple-client-id',
      APPLE_CLIENT_SECRET: 'fake-apple-client-secret',
      OTEL_SDK_DISABLED: 'true',
    },

    // Disable global setup for unit tests
    globalSetup: undefined,

    // Test files - only include unit tests
    include: ['**/__tests__/unit/**/*.{test,spec}.ts'],
    exclude: ['**/node_modules/**', '**/dist/**'],

    // Timeouts
    testTimeout: 10000, // 10 seconds
    hookTimeout: 10000, // 10 seconds

    // Coverage configuration
    coverage: {
      // Don't enable coverage by default, but allow it when --coverage flag is used
      enabled: undefined, // Let vitest determine based on CLI flags
      provider: 'istanbul',
      include: ['src/**/*.ts'],
      exclude: [
        'src/**/*.test.ts',
        'src/**/*.spec.ts',
        'src/shared/test-utils/**',
        'src/**/*.d.ts',
        'src/index.ts', // Main entry point
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
      },
    },

    // Use default reporter with no summary for cleaner output during development
    reporters: process.env.CI 
      ? ['verbose'] 
      : [['default', { summary: false }]],

    // Reduce verbosity of test output
    logHeapUsage: false,

    // Only show errors and failures, not successful tests details
    hideSkippedTests: true,

    // Silent mode for logger/console outputs during tests
    silent: false, // Keep false so we can see unexpected errors

    // Pool options for better performance and less noise
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
        isolate: true,
        useAtomics: true,
      },
    },

    // Faster bail-out on failures in development
    bail: process.env.CI ? 0 : 1,
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
    },
  },
});
